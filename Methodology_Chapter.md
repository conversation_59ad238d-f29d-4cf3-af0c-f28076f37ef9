# CHAPTER THREE: METHODOLOGY

## 3.1 Introduction

This chapter outlines the methodology employed in the design and development of the AI-Powered Metadata Harvesting System. The system is designed to automate the generation of high-quality, FAIR-compliant metadata from raw datasets. The methodology follows a structured approach, encompassing system architecture design, data processing pipelines, user interface development, and a comprehensive evaluation framework. Each phase of the methodology is aligned with the project's objectives to ensure all requirements are met.

## 3.2 System Architecture

The system is built upon a robust and scalable architecture that separates concerns and allows for efficient processing of data. The architecture consists of several key components that work together to deliver the system's functionality.

```plantuml
@startuml
!theme vibrant
skinparam componentStyle rectangle

package "User Interface" {
  [Web Browser]
}

package "Application Server (Flask)" {
  [Web Routes]
  [Authentication]
  [Application Logic]
}

package "Background Processing" {
  [Celery Workers]
  [Processing Service]
}

package "Data & Caching" {
  database "MongoDB" {
    [Datasets]
    [Users]
    [Metadata]
  }
  [Redis]
}

package "AI & NLP Services" {
  [NLP Service (spaCy, BERT)]
  [Metadata Generation]
  [Semantic Search Service]
}

[Web Browser] --> [Web Routes] : HTTP Requests
[Web Routes] --> [Application Logic]
[Application Logic] --> [Authentication]
[Application Logic] --> "MongoDB" : Read/Write Data
[Application Logic] --> [Celery Workers] : Enqueue Tasks

[Celery Workers] --> [Processing Service] : Execute Tasks
[Processing Service] --> [AI & NLP Services] : Analyze Data
[AI & NLP Services] --> "MongoDB" : Store Results
[Celery Workers] --> [Redis] : Task Queue

@enduml
```
**Figure 3.1: System Architecture Diagram**

The architecture is composed of:
-   **User Interface:** A web-based interface built with HTML, CSS, and JavaScript that allows users to interact with the system.
-   **Application Server:** A Flask-based backend that handles user requests, manages application logic, and communicates with the database.
-   **Background Processing:** A task queue system using Celery and Redis to handle long-running processes like metadata generation and NLP analysis asynchronously.
-   **Data & Caching:** MongoDB is used as the primary database for storing user data, datasets, and metadata. Redis serves as a message broker for Celery and for caching.
-   **AI & NLP Services:** A collection of services that perform the core AI and NLP tasks, including metadata extraction, semantic analysis, and quality assessment.

## 3.3 Data Collection and User Interface

This section details the methodology for achieving the first objective: establishing a secure and user-friendly data collection mechanism.

The system provides a web interface that allows researchers to upload their datasets securely. The interface is designed to be intuitive, guiding the user through the process of creating a new dataset entry and uploading the corresponding file.

-   **Technologies Used:** The frontend is developed using **HTML5**, **CSS3**, and **JavaScript**. The backend is powered by the **Python Flask** framework, which handles file uploads, form processing, and user authentication.
-   **Secure Data Upload:** When a user uploads a file, it is sent to the Flask server via a secure HTTP POST request. The server uses `werkzeug.utils.secure_filename` to ensure the filename is safe before saving it to a dedicated `uploads` directory.
-   **Supported Formats:** The system is designed to accept raw tabular data in various formats, including **CSV**, **Excel (XLSX)**, and **JSON**.

## 3.4 Automated Metadata Extraction

This section addresses the second objective: implementing an automated metadata extraction module using NLP techniques.

Once a dataset is uploaded, it is placed in a processing queue. A background worker then picks up the dataset and initiates the metadata extraction process. This process involves several NLP techniques to extract both descriptive and structural metadata.

-   **NLP Pipeline:** The system uses a pipeline of NLP services to analyze the dataset's content.
    -   **Named Entity Recognition (NER):** **spaCy** is used to identify and categorize named entities such as persons, organizations, and locations within the data.
    -   **Keyword Extraction:** **TF-IDF (Term Frequency-Inverse Document Frequency)** is employed to identify the most significant keywords and terms in the dataset.
    -   **Semantic Analysis:** **BERT (Bidirectional Encoder Representations from Transformers)** models are used to understand the semantic context of the data, which aids in generating more accurate descriptions and tags.
-   **Structural Metadata:** The system also extracts structural metadata, such as column names, data types, and the number of records and fields.

```plantuml
@startuml
!theme vibrant
title Automated Metadata Extraction Flow

start
:User uploads dataset;
:Task enqueued in Celery/Redis;
:Celery worker picks up task;
fork
  :Extract Structural Metadata;
fork again
  :Extract Descriptive Content;
end fork
:Perform NLP Analysis;
:Store Extracted Raw Metadata;
stop
@enduml
```
**Figure 3.4: Automated Metadata Extraction Flowchart**

## 3.5 Metadata Processing and Storage

This section covers the third objective: designing a service to clean, validate, and store the extracted metadata.

The raw metadata extracted by the NLP module undergoes a cleaning and transformation process to ensure it is accurate and compliant with established standards.

-   **Data Cleaning:** A `DataCleaningService` handles tasks like removing special characters, standardizing formats, and handling missing values.
-   **Standard Compliance:** The cleaned metadata is then transformed to comply with:
    -   **Schema.org:** A standard vocabulary for structured data on the internet.
    -   **FAIR Principles:** A set of guiding principles to make data Findable, Accessible, Interoperable, and Reusable.
-   **Persistent Storage:** The final, validated metadata is stored in a **MongoDB** database. MongoDB's flexible, document-oriented structure is well-suited for storing the rich and varied metadata generated by the system.

```plantuml
@startuml
!theme vibrant
title Metadata Processing and Storage Flow

start
:Receive Raw Extracted Metadata;
note right: Python Service

:Begin Data Cleaning;
partition "Data Cleaning Service (Python)" {
  :Remove special characters;
  :Standardize data formats;
  :Handle missing values;
}

:Begin Metadata Transformation;
partition "Metadata Transformation (Python)" {
  :Map to Schema.org vocabulary;
  :Assess and align with FAIR Principles;
}

:Validate Metadata;
note right: Python (Pydantic)

if (Is Valid?) then (yes)
  :Persist to Database;
  note right: MongoDB
else (no)
  :Log validation error;
  :Flag for manual review;
endif

stop
@enduml
```
**Figure 3.5: Metadata Processing and Storage Flowchart**

## 3.6 Semantic Search Engine

This section details the methodology for the fourth objective: integrating a robust semantic search engine.

To enable precise and context-aware dataset discovery, the system implements a semantic search engine that goes beyond simple keyword matching.

-   **Vector Embeddings:** The `SemanticSearchService` uses a pre-trained **BERT** model (from the `sentence-transformers` library) to convert the titles, descriptions, and tags of all datasets into high-dimensional vector embeddings.
-   **Indexing:** These embeddings are stored in an in-memory index (using a library like FAISS or a similar vector search library) for fast retrieval.
-   **Querying:** When a user performs a search, their query is also converted into a vector embedding. The system then calculates the cosine similarity between the query vector and the vectors of all datasets in the index. The datasets with the highest similarity scores are returned as the search results.

```plantuml
@startuml
!theme vibrant
title Semantic Search Engine Flow

start
:User submits search query;
note right: Web Interface (JS/HTML)

:Query sent to Flask Backend;
note right: Python (Flask)

:Generate Vector Embedding for Query;
note right: BERT (sentence-transformers)

:Search Vector Index for Similar Datasets;
note right: FAISS / Vector Library

:Retrieve Top-K most similar dataset IDs;

:Fetch full dataset details from database;
note right: MongoDB

:Return search results to user;

stop
@enduml
```
**Figure 3.6: Semantic Search Engine Flowchart**

## 3.7 Metadata Visualization and Export

This section addresses the fifth objective: implementing functionalities for metadata visualization and export.

The system provides tools for users to visualize the characteristics of their datasets and to export the generated metadata in various formats.

-   **Visualization:** The web application uses **Chart.js**, a powerful and flexible JavaScript charting library, to create interactive visualizations. These charts display information such as data quality scores, FAIR compliance metrics, and data type distributions.
-   **Export Formats:** Users can export the metadata in several machine-readable formats to facilitate interoperability with other systems. Supported formats include:
    -   **JSON-LD (JSON for Linked Data)**
    -   **Markdown**
    -   **PDF**
    -   **JSON**

## 3.8 System Evaluation

This section outlines the methodology for the sixth objective: evaluating the end-to-end performance of the framework.

A comprehensive evaluation framework is designed to assess the system's performance across several key aspects.

-   **Accuracy of Metadata:**
    -   **NLP Extraction Accuracy:** The accuracy of the NLP module will be measured using standard metrics: **precision, recall, and F1-score**. This will be done by comparing the automatically extracted metadata against a manually curated "gold standard" set.
    -   **Standard Compliance Score:** The system's `QualityAssessmentService` automatically calculates a compliance score for both **Schema.org** and the **FAIR principles**.
-   **System Performance and Efficiency:**
    -   **Metadata Generation Time:** The time taken to process datasets of varying sizes will be recorded and analyzed.
    -   **Search Query Response Time:** The time taken to return search results for various queries will be measured.
-   **Search Relevance:**
    -   **Search Result Quality:** The relevance of the search results will be quantified using **Mean Average Precision (MAP)** and **Normalized Discounted Cumulative Gain (NDCG)**.
-   **Scalability and Reliability:**
    -   **Concurrent Task Handling:** The system's ability to handle multiple simultaneous uploads and processing tasks will be tested.
-   **Large File Processing:** The system will be tested with large dataset files to ensure it can handle them without crashing.

### 3.8.1 Sample Evaluation Data

To provide a concrete example of the evaluation process, this section presents sample data that could be generated during the evaluation.

**Table 3.1: NLP Extraction Accuracy**

| Metric    | Precision | Recall | F1-Score |
|-----------|-----------|--------|----------|
| **Value** | 0.92      | 0.89   | 0.90     |

**Table 3.2: Standard Compliance Score**

| Standard      | Compliance Score |
|---------------|------------------|
| **Schema.org**| 95%              |
| **FAIR**      | 88%              |

**Table 3.3: System Performance and Efficiency**

| Metric                      | Dataset Size (Rows) | Value          |
|-----------------------------|---------------------|----------------|
| **Metadata Generation Time**| 10,000              | 45 seconds     |
|                             | 100,000             | 8 minutes      |
|                             | 1,000,000           | 35 minutes     |
| **Search Query Response Time**| N/A                 | 1.5 seconds    |

**Table 3.4: Search Relevance**

| Metric | Value  |
|--------|--------|
| **MAP**| 0.85   |
| **NDCG**| 0.91   |

**Table 3.5: Scalability and Reliability**

| Metric                      | Test Condition              | Result         |
|-----------------------------|-----------------------------|----------------|
| **Concurrent Task Handling**| 5 simultaneous uploads      | All successful |
|                             | 10 simultaneous uploads     | All successful |
| **Large File Processing**   | 1 GB file                   | Successful     |
|                             | 5 GB file                   | Successful     |

## 3.9 Input and Output Design

### 3.9.1 Input Design

The primary input to the system is a raw dataset file uploaded by a user. The system is designed to accept the following input formats:
-   **CSV (Comma-Separated Values):** A plain text format for tabular data.
-   **Excel (XLSX):** The Microsoft Excel spreadsheet format.
-   **JSON (JavaScript Object Notation):** A lightweight data-interchange format.

In addition to the file itself, the user provides initial metadata through a web form, including a title, a brief description, and relevant tags.

### 3.9.2 Output Design

The system produces several outputs:
-   **Enhanced Metadata:** A rich set of descriptive and structural metadata, compliant with Schema.org and FAIR principles.
-   **Data Quality Reports:** Detailed reports on the quality and completeness of the dataset.
-   **Interactive Visualizations:** Charts and graphs that provide insights into the dataset's characteristics.
-   **Exportable Files:** The generated metadata can be exported in various formats (JSON-LD, Markdown, PDF, JSON).
-   **Downloadable Packages:** Users can download a ZIP archive containing the original dataset, all generated metadata, and reports.

## 3.10 Use Case Diagram

The following use case diagram illustrates the interactions between the users (actors) and the system.

```plantuml
@startuml
!theme vibrant
left to right direction

actor "Researcher" as researcher
actor "System Admin" as admin

rectangle "AI-Powered Metadata Harvesting System" {
  usecase "Upload Dataset" as UC1
  usecase "View Dashboard" as UC2
  usecase "Search for Datasets" as UC3
  usecase "View Dataset Details" as UC4
  usecase "View Quality Report" as UC5
  usecase "Export Metadata" as UC6
  usecase "Manage Users" as UC7
  usecase "Monitor System" as UC8
}

researcher -- UC1
researcher -- UC2
researcher -- UC3
researcher -- UC4
researcher -- UC5
researcher -- UC6

admin -- UC7
admin -- UC8
admin -- UC3
admin -- UC4

@enduml
```
**Figure 3.2: Use Case Diagram**

## 3.11 Data Flow Diagram

The data flow diagram below illustrates the movement of data through the system, from the initial upload to the final storage and retrieval.

```plantuml
@startuml
!theme vibrant

cloud "User" as user
queue "Processing Queue" as queue
database "MongoDB" as db

package "Flask Application" {
  [Upload Interface]
  [Search Interface]
  [Display Interface]
}

package "Processing Pipeline" {
  [Data Extractor]
  [NLP Analyzer]
  [Metadata Generator]
  [Quality Assessor]
}

user -> [Upload Interface] : Uploads Dataset
[Upload Interface] -> queue : Enqueues Task
queue -> [Data Extractor] : Starts Processing
[Data Extractor] -> [NLP Analyzer] : Raw Data
[NLP Analyzer] -> [Metadata Generator] : Analyzed Data
[Metadata Generator] -> [Quality Assessor] : Generated Metadata
[Quality Assessor] -> db : Stores Final Metadata & Report

user -> [Search Interface] : Enters Search Query
[Search Interface] -> db : Queries Metadata
db -> [Display Interface] : Returns Results
[Display Interface] -> user : Displays Results

@enduml
```
**Figure 3.3: Data Flow Diagram**

## 3.12 NLP Processing Pipeline

The Natural Language Processing (NLP) pipeline is at the heart of the metadata extraction module. It is a multi-stage process designed to analyze the textual content of datasets and extract meaningful information. Each stage builds upon the previous one to create a comprehensive set of raw metadata, which is then used for description generation, quality scoring, and semantic search indexing. The pipeline is implemented in Python, leveraging a combination of open-source libraries to achieve its goals.

```plantuml
@startuml
!theme vibrant
title NLP Processing Pipeline

start
:Input: Raw text content from dataset;
note right: Python Service

:Stage 1: Text Cleaning;
- Remove special characters
- Convert to lowercase
- Tokenize text
note right: Python (re, NLTK)

:Stage 2: Keyword & Tag Extraction;
- Calculate TF-IDF scores
- Identify top N keywords
- Suggest relevant tags
note right: scikit-learn

:Stage 3: Named Entity Recognition (NER);
- Identify entities (e.g., Person, Org, Date)
note right: spaCy

:Stage 4: Semantic Analysis;
- Generate sentence embeddings
- Analyze sentiment
note right: BERT (Transformers)

:Output: Structured NLP Results;
- Keywords
- Entities
- Sentiment Score
- Summary
note right: JSON object

stop
@enduml
```
**Figure 3.12: NLP Processing Pipeline Flowchart**

## 3.13 Background Processing Pipeline

To ensure the web application remains responsive and can handle time-consuming tasks without freezing, the system implements a background processing pipeline using Celery and Redis. When a user uploads a large dataset, the processing task is offloaded to a background worker, allowing the user to continue interacting with the application. This architecture is crucial for scalability and reliability.

```plantuml
@startuml
!theme vibrant
title Background Processing Pipeline

actor User
box "Flask Web App"
  participant "Web Route" as Route
end box
box "Task Queue System"
  participant "Redis" as RedisQueue
  participant "Celery Worker" as Worker
end box
box "Processing Services"
  participant "Main Service" as ProcService
end box

User -> Route : Uploads Dataset
Route -> RedisQueue : Enqueue Task
RedisQueue -> Worker : Delivers Task
Worker -> ProcService : Executes Task
ProcService -> ProcService : ...performs processing...
ProcService -> Worker : Returns Result
Worker -> RedisQueue : Updates Status
@enduml
```
**Figure 3.13: Background Processing Pipeline**

## 3.14 Quality Scoring Methodology

The system employs a quantitative approach to assess the quality of each dataset based on several key dimensions. The Quality Scoring service calculates a weighted score that reflects the dataset's completeness, consistency, and accuracy. This score provides a quick and understandable metric for researchers to evaluate a dataset's reliability and usefulness.

```plantuml
@startuml
!theme vibrant
title Quality Scoring Flow

start
:Input: Dataset object and data;
:Calculate Completeness Score;
:Calculate Consistency Score;
:Calculate Accuracy Score;
:Calculate Final Weighted Score;
:Output: Quality Score (0-100);
stop
@enduml
```
**Figure 3.14: Quality Scoring Flowchart**

## 3.15 FAIR Principles Scoring

In addition to a general quality score, the system specifically evaluates each dataset against the FAIR (Findable, Accessible, Interoperable, Reusable) principles. This is crucial for promoting open science and ensuring that the data is truly valuable to the broader research community. The scoring is based on a checklist of criteria for each of the four principles.

```plantuml
@startuml
!theme vibrant
title FAIR Principles Scoring Flow

start
:Input: Dataset Metadata;
:Evaluate 'Findable';
:Evaluate 'Accessible';
:Evaluate 'Interoperable';
:Evaluate 'Reusable';
:Calculate Overall FAIR Score;
:Output: FAIR Score and component scores;
stop
@enduml
```
**Figure 3.15: FAIR Scoring Flowchart**

## 3.16 Task Queue Design

The task queue is the backbone of the background processing system. It is designed to manage and distribute tasks to available workers, ensuring that processing jobs are handled efficiently and in the correct order. The system uses Redis as a message broker to hold the queue of tasks and Celery as the distributed task queue framework.

```plantuml
@startuml
!theme vibrant
title Task Queue Design

cloud "Web App (Producer)" as Producer
queue "Redis (Broker)" as Queue
database "MongoDB (Result Backend)" as Results

Producer -> Queue : Enqueues Task
Queue --> [Celery Workers]
[Celery Workers] -> Results : Store Result
Producer -> Results : Fetch Status

@enduml
```
**Figure 3.16: Task Queue Architecture**

## 3.17 Database Design

The database schema is designed using MongoEngine, an Object-Document Mapper (ODM) for MongoDB. This provides a flexible yet structured way to store the application's data. The core models are `User`, `Dataset`, and `MetadataQuality`, which are linked together to create a comprehensive record of each dataset and its associated information.

```plantuml
@startuml
!theme vibrant
title Database Schema (MongoDB)

object User {
  id: ObjectId
  username: String
  email: String
  is_admin: Boolean
}

object Dataset {
  id: ObjectId
  title: String
  description: String
  source_url: String
  tags: ListField
  user: ReferenceField(User)
  status: String
  --
  nlp_results: DictField
  quality_score: FloatField
  fair_score: FloatField
}

object MetadataQuality {
  id: ObjectId
  dataset: ReferenceField(Dataset)
  completeness: FloatField
  consistency: FloatField
  accuracy: FloatField
  --
  fair_findable: FloatField
  fair_accessible: FloatField
  fair_interoperable: FloatField
  fair_reusable: FloatField
}

User "1" -- "0..*" Dataset : uploads
Dataset "1" -- "1" MetadataQuality : has

@enduml
```
**Figure 3.17: Database Design Diagram**

## 3.18 Conclusion

The methodology described in this chapter provides a comprehensive framework for the development and evaluation of the AI-Powered Metadata Harvesting System. By combining a robust system architecture with advanced AI and NLP techniques, the project aims to deliver a powerful tool for researchers and data scientists. The structured approach ensures that all objectives are met and that the final system is accurate, efficient, and scalable.
